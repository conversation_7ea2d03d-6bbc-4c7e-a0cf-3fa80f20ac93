import SwiftUI

struct LiveMatchView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var flicManager: FlicManager
    @Environment(\.presentationMode) var presentationMode

    @State private var match: Match
    @State private var showingGameCompleteAlert = false
    @State private var showingGameWinCover = false
    @State private var gameWinnerText = ""
    @State private var showingMatchWinCover = false
    @State private var matchWinnerText = ""
    @State private var showingCancelAlert = false
    @State private var notifications: [String] = []
    @State private var showingServiceSelection = false
    @State private var pendingGameNumber: Int?
    @State private var serviceSelectionCompleted = false
    @State private var showingPauseMenu = false

    init(match: Match) {
        var initialMatch = match
        let statusChanged = initialMatch.status != .inProgress
        initialMatch.status = .inProgress

        // Check if we need to show service selection
        let needsServiceSelection: Bool
        let gameNumber: Int

        if initialMatch.games.isEmpty {
            // Geen games - toon service selectie voor game 1
            needsServiceSelection = true
            gameNumber = 1
            print("🏓 SERVICE SELECTION: No games - showing service selection for game 1")
        } else {
            // Check if there are games without any score history (fresh games)
            let gamesWithoutScores = initialMatch.games.filter { $0.scoreHistory.isEmpty }
            if !gamesWithoutScores.isEmpty {
                // Er zijn games zonder scores - toon service selectie voor de eerste
                needsServiceSelection = true
                gameNumber = gamesWithoutScores.first!.gameNumber
                print("🏓 SERVICE SELECTION: Found \(gamesWithoutScores.count) games without scores - showing service selection for game \(gameNumber)")
            } else {
                // Alle games hebben al scores - geen service selectie nodig
                needsServiceSelection = false
                gameNumber = 0
                print("🏓 SERVICE SELECTION: All games have scores - no service selection needed")
            }
        }

        self._match = State(initialValue: initialMatch)
        self._showingServiceSelection = State(initialValue: needsServiceSelection)
        self._pendingGameNumber = State(initialValue: needsServiceSelection ? gameNumber : nil)

        // Als de status is gewijzigd, sla de match op
        if statusChanged {
            print("🏓 LIVE MATCH: Status changed to inProgress, will save match")
        }
    }

    private var currentGame: Game? {
        let game: Game?
        if match.type == .mixMatch {
            // Voor Mix & Match: vind de eerste niet-voltooide game
            game = match.games.first { !$0.isCompleted }
        } else {
            // Voor reguliere wedstrijden: vind de huidige game op basis van gameNumber
            let currentGameNumber = getCurrentGameNumber()
            game = match.games.first { $0.gameNumber == currentGameNumber }
            print("🎮 CURRENT GAME SEARCH: Looking for game \(currentGameNumber), found: \(game != nil)")
        }

        if let game = game {
            print("🎮 CURRENT GAME: Game \(game.gameNumber) - Score: \(game.team1Score)-\(game.team2Score)")
        } else {
            print("🎮 CURRENT GAME: No current game found")
        }

        return game
    }

    private func getCurrentGameNumber() -> Int {
        print("🎮 GET CURRENT GAME NUMBER: Total games = \(match.games.count)")

        if match.games.isEmpty {
            print("🎮 GET CURRENT GAME NUMBER: No games, returning 1")
            return 1
        }

        // Debug: print all games
        for (index, game) in match.games.enumerated() {
            print("🎮 GET CURRENT GAME NUMBER: Game[\(index)] = gameNumber:\(game.gameNumber), scoreHistory:\(game.scoreHistory.count), completed:\(game.isCompleted)")
        }

        if match.type == .mixMatch {
            // Voor Mix & Match: vind de eerste niet-voltooide game
            if let incompleteGame = match.games.first(where: { !$0.isCompleted }) {
                print("🎮 GET CURRENT GAME NUMBER: Mix & Match - found incomplete game: \(incompleteGame.gameNumber)")
                return incompleteGame.gameNumber
            }
        } else {
            // Voor reguliere wedstrijden: gebruik dezelfde logica als service selection
            // Vind de hoogste voltooide game en return de volgende
            let completedGames = match.games.filter { $0.isCompleted }
            let currentGameNumber = completedGames.count + 1
            print("🎮 GET CURRENT GAME NUMBER: Regular match - completed games: \(completedGames.count), current game: \(currentGameNumber)")
            return currentGameNumber
        }

        // Fallback: return 1
        print("🎮 GET CURRENT GAME NUMBER: Fallback, returning 1")
        return 1
    }

    var body: some View {
        GeometryReader { geometry in
            let isLandscape = geometry.size.width > geometry.size.height
            let isPhone = UIDevice.current.userInterfaceIdiom == .phone

            if isLandscape {
                if isPhone {
                    // iPhone Split-Screen Layout
                    iphoneSplitScreenLayout(game: currentGame, geometry: geometry)
                } else {
                    // iPad Landscape Layout (existing)
                    ipadLandscapeLayout(game: currentGame, geometry: geometry)
                }
            } else {
                // Portrait Layout (original)
                portraitLayout(game: currentGame, isPhone: isPhone)
            }

            // Notifications - Show in both orientations
            if !notifications.isEmpty {
                VStack(alignment: .center, spacing: 12) {
                    ForEach(notifications, id: \.self) { notification in
                        Text(notification)
                            .font(.system(size: 22, weight: .semibold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 16)
                            .background(Color.blue)
                            .cornerRadius(16)
                    }
                }
                .padding(.horizontal, 40)
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        notifications.removeAll()
                    }
                }
            }
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .onAppear {
            // Disable idle timer to keep screen on during live match
            UIApplication.shared.isIdleTimerDisabled = true
            print("🔒 Screen idle timer disabled - screen will stay on during live match")

            // Force landscape orientation on iPhone for better live match experience
            if UIDevice.current.userInterfaceIdiom == .phone {
                AppDelegate.orientationLock = .landscape
                UIDevice.current.setValue(UIInterfaceOrientation.landscapeRight.rawValue, forKey: "orientation")
                UINavigationController.attemptRotationToDeviceOrientation()
            }

            // Setup Flic button integration
            setupFlicButtons()

            // Save match status update if it was changed to inProgress
            if match.status == .inProgress {
                print("🏓 LIVE MATCH: Saving status update to inProgress")
                dataManager.updateMatch(match)
            }

            // Check if we need to show service selection for existing matches
            checkForPendingServiceSelection()
        }
        .onDisappear {
            // Re-enable idle timer when leaving live match
            UIApplication.shared.isIdleTimerDisabled = false
            print("🔓 Screen idle timer re-enabled - screen can now turn off normally")

            // Reset orientation lock when leaving live match
            if UIDevice.current.userInterfaceIdiom == .phone {
                AppDelegate.orientationLock = .all
            }

            // Cleanup Flic button integration
            flicManager.stopLiveMatch()
        }
        .fullScreenCover(isPresented: $showingGameWinCover) {
            GameWinCoverView(
                winnerText: gameWinnerText,
                onDismiss: {
                    showingGameWinCover = false
                    if match.isCompleted {
                        // Set match winner text and show match win cover
                        if match.type == .mixMatch {
                            // Voor Mix & Match: gebruik de daadwerkelijke winnaar(s)
                            let winners = match.mixMatchWinners
                            if winners.count == 1 {
                                matchWinnerText = "\(winners[0].name) wins the match!"
                            } else if winners.count > 1 {
                                let winnerNames = winners.map { $0.name }.joined(separator: " & ")
                                matchWinnerText = "\(winnerNames) share the victory!"
                            } else {
                                matchWinnerText = "Match completed!"
                            }
                        } else if let winner = match.winner {
                            let winnerName = getTeamDisplayName(for: winner)
                            matchWinnerText = "\(winnerName) wins the match!"
                        }
                        showingMatchWinCover = true
                    } else {
                        // Voor Mix & Match: announce next teams before starting next game
                        if match.type == .mixMatch {
                            announceNextMixMatchTeams()
                        }
                        startNextGame()
                    }
                }
            )
        }
        .fullScreenCover(isPresented: $showingMatchWinCover) {
            MatchWinCoverView(
                winnerText: matchWinnerText,
                onDismiss: {
                    // Re-enable idle timer when match is complete
                    UIApplication.shared.isIdleTimerDisabled = false
                    print("🔓 Screen idle timer re-enabled - match completed")

                    showingMatchWinCover = false
                    dataManager.updateMatch(match)
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .alert("Stop Match", isPresented: $showingCancelAlert) {
            Button("Stop", role: .destructive) {
                // Re-enable idle timer when stopping match
                UIApplication.shared.isIdleTimerDisabled = false
                print("🔓 Screen idle timer re-enabled - match stopped")

                presentationMode.wrappedValue.dismiss()
            }
            Button("Continue", role: .cancel) { }
        } message: {
            Text("Are you sure you want to stop the match? Progress will be lost.")
        }
        .actionSheet(isPresented: $showingPauseMenu) {
            ActionSheet(
                title: Text("Pause Menu"),
                buttons: [
                    .default(Text("Undo Last Point")) {
                        undoLastPoint()
                    },
                    .destructive(Text("Stop Match")) {
                        showingCancelAlert = true
                    },
                    .cancel(Text("Continue"))
                ]
            )
        }
        .overlay(
            // Service Selection Overlay
            ZStack {
                if showingServiceSelection && !serviceSelectionCompleted, let gameNumber = pendingGameNumber {
                    ServiceSelectionView(
                        gameNumber: gameNumber,
                        team1Name: getTeamDisplayName(for: .team1),
                        team2Name: getTeamDisplayName(for: .team2),
                        onTeamSelected: { selectedTeam in
                            print("🎯 SERVICE TEAM SELECTED: \(selectedTeam)")
                            selectServiceTeam(selectedTeam, for: gameNumber)
                        }
                    )
                    .onAppear {
                        print("🎯 SERVICE SELECTION OVERLAY: Showing ServiceSelectionView for game \(gameNumber)")
                        print("🎯 SHOWING SERVICE SELECTION: Game \(gameNumber)")
                    }
                }
            }
        )
    }

    // MARK: - Game Logic Functions

    private func addPoint(to team: Team) {
        print("🚨 ADD POINT CALLED: Team \(team)")

        guard let gameIndex = match.games.firstIndex(where: { !$0.isCompleted }) else {
            print("⚽ ADD POINT: No active game found")
            return
        }

        print("⚽ ADD POINT: Adding point for \(team) to game \(gameIndex + 1)")
        print("⚽ ADD POINT: Score before: \(match.games[gameIndex].team1Score)-\(match.games[gameIndex].team2Score)")

        // Update game with new point
        match.games[gameIndex].addPoint(to: team, matchType: match.type)
        let updatedGame = match.games[gameIndex]

        print("⚽ ADD POINT: Score after: \(updatedGame.team1Score)-\(updatedGame.team2Score)")
        print("⚽ ADD POINT: Match state updated - triggering UI refresh")

        // Play point sound effect
        SoundManager.shared.playPointSound()

        print("⚽ ADD POINT: Game \(gameIndex + 1) score history now has \(updatedGame.scoreHistory.count) entries")

        // Save the updated match to DataManager after each point
        dataManager.updateMatch(match)

        print("⚽ ADD POINT: Match updated in DataManager")

        // Check if game is completed
        print("🏆 GAME WIN CHECK: Game \(updatedGame.gameNumber) completed: \(updatedGame.isCompleted)")
        if updatedGame.isCompleted {
            print("🏆 GAME WIN: Game \(updatedGame.gameNumber) is completed!")
            if let winner = updatedGame.winner {
                let winnerName = getTeamDisplayName(for: winner, game: updatedGame)
                addNotification("\(winnerName) wins game \(updatedGame.gameNumber)!")
                print("🏆 GAME WIN: Winner is \(winnerName)")

                // Play game win sound effect
                SoundManager.shared.playGameWinSound()

                // Check if match is completed
                print("🏆 MATCH WIN CHECK: Match completed: \(match.isCompleted)")
                if match.isCompleted {
                    // First update the match in DataManager while status is still inProgress
                    // This ensures ELO calculations are triggered when status changes to completed
                    dataManager.updateMatch(match)

                    // Now set status to completed and update again
                    match.status = .completed
                    match.completedAt = Date()

                    // Play match win sound effect
                    SoundManager.shared.playMatchWinSound()

                    // Set match winner text and show match win cover
                    if match.type == .mixMatch {
                        // Voor Mix & Match: gebruik de daadwerkelijke winnaar(s)
                        let winners = match.mixMatchWinners
                        if winners.count == 1 {
                            matchWinnerText = "\(winners[0].name) wins the match!"
                        } else if winners.count > 1 {
                            let winnerNames = winners.map { $0.name }.joined(separator: " & ")
                            matchWinnerText = "\(winnerNames) share the victory!"
                        } else {
                            matchWinnerText = "Match completed!"
                        }
                    } else if let winner = match.winner {
                        let winnerName = getTeamDisplayName(for: winner)
                        matchWinnerText = "\(winnerName) wins the match!"
                    }
                    showingMatchWinCover = true
                    print("🏆 MATCH WIN: Showing match win cover")
                } else {
                    // Set winner text and show fullscreen cover
                    // Use the completed game (updatedGame) instead of currentGame
                    if let winner = updatedGame.winner {
                        let winnerName = getTeamDisplayName(for: winner, game: updatedGame)
                        gameWinnerText = "\(winnerName) wins game \(updatedGame.gameNumber)!"
                    }
                    showingGameWinCover = true
                    print("🏆 GAME WIN: Showing game win cover for game \(updatedGame.gameNumber)")
                }
            } else {
                print("🏆 GAME WIN: No winner found for completed game \(updatedGame.gameNumber)")
            }
        } else {
            print("🏆 GAME WIN CHECK: Game \(updatedGame.gameNumber) not completed yet")
        }
    }

    private func undoLastPoint() {
        guard canUndoPoint() else {
            print("🔄 UNDO: Cannot undo - no points available")
            return
        }

        // Find the game with the most recent score entry
        var mostRecentGameIndex: Int?
        var mostRecentTimestamp: Date?

        for (index, game) in match.games.enumerated() {
            if let lastScore = game.scoreHistory.last {
                if mostRecentTimestamp == nil || lastScore.timestamp > mostRecentTimestamp! {
                    mostRecentTimestamp = lastScore.timestamp
                    mostRecentGameIndex = index
                }
            }
        }

        guard let gameIndex = mostRecentGameIndex else {
            print("🔄 UNDO: No game found with score history")
            return
        }

        print("🔄 UNDO: Attempting to undo from game \(gameIndex + 1)")
        let success = match.games[gameIndex].undoLastPoint()

        if success {
            print("🔄 UNDO: Successfully undid point from game \(gameIndex + 1)")
            addNotification("Point undone")
            // Save the updated match to DataManager
            dataManager.updateMatch(match)
            print("🔄 UNDO: Match updated in DataManager")
        } else {
            print("🔄 UNDO: Failed to undo point from game \(gameIndex + 1)")
        }
    }

    private func canUndoPoint() -> Bool {
        let hasUndoablePoints = match.games.contains { game in
            !game.scoreHistory.isEmpty
        }

        print("🔄 UNDO Check: Can undo = \(hasUndoablePoints)")
        for (index, game) in match.games.enumerated() {
            print("🔄 Game \(index + 1): Score history count = \(game.scoreHistory.count)")
        }

        return hasUndoablePoints
    }

    private func startNextGame() {
        let nextGameNumber: Int

        if match.type == .mixMatch {
            // Voor Mix & Match zijn alle games al gegenereerd, we hoeven alleen de volgende te activeren
            // Vind de volgende niet-voltooide game
            if let nextGame = match.games.first(where: { !$0.isCompleted }) {
                nextGameNumber = nextGame.gameNumber
                print("🏓 SERVICE SELECTION: Mix & Match - next game is \(nextGameNumber)")
            } else {
                print("🏓 SERVICE SELECTION: Mix & Match - no more games")
                return // Geen games meer
            }
        } else {
            // Voor reguliere wedstrijden: gebruik dezelfde logica als getCurrentGameNumber
            let completedGames = match.games.filter { $0.isCompleted }
            nextGameNumber = completedGames.count + 1
            print("🏓 SERVICE SELECTION: Regular match - completed games: \(completedGames.count), next game: \(nextGameNumber)")
        }

        // Toon service selectie voor de nieuwe game
        pendingGameNumber = nextGameNumber
        showingServiceSelection = true
        serviceSelectionCompleted = false  // Reset voor nieuwe game
        print("🏓 SERVICE SELECTION: Showing service selection for game \(nextGameNumber)")
        print("🏓 SERVICE SELECTION: showingServiceSelection = \(showingServiceSelection)")
        print("🏓 SERVICE SELECTION: pendingGameNumber = \(pendingGameNumber)")
        print("🏓 SERVICE SELECTION: serviceSelectionCompleted = \(serviceSelectionCompleted)")
        print("🏓 SERVICE SELECTION: showingGameWinCover = \(showingGameWinCover)")
    }

    private func selectServiceTeam(_ team: Team, for gameNumber: Int) {
        print("🏓 SERVICE SELECTION: Team \(team) selected for game \(gameNumber)")

        if match.type == .mixMatch {
            // Voor Mix & Match: update de bestaande game
            if let gameIndex = match.games.firstIndex(where: { $0.gameNumber == gameNumber && !$0.isCompleted }) {
                match.games[gameIndex].currentServer = team
                match.games[gameIndex].currentServerPlayer = GameRulesEngine.initialServerPlayer(for: gameNumber, team: team)
                print("🏓 SERVICE SELECTION: Updated Mix & Match game \(gameNumber) with server \(team)")
            } else {
                print("🏓 SERVICE SELECTION: Could not find Mix & Match game \(gameNumber)")
            }
        } else {
            // Voor reguliere wedstrijden: update bestaande game in plaats van nieuwe aanmaken
            if let gameIndex = match.games.firstIndex(where: { $0.gameNumber == gameNumber }) {
                print("🏓 SERVICE SELECTION: Found existing game \(gameNumber) at index \(gameIndex) - updating server")
                match.games[gameIndex].currentServer = team
                match.games[gameIndex].currentServerPlayer = GameRulesEngine.initialServerPlayer(for: gameNumber, team: team)
                print("🏓 SERVICE SELECTION: Updated existing game \(gameNumber) with server \(team)")
            } else {
                // Alleen als er geen bestaande game is, maak een nieuwe aan
                var nextGame = GameRulesEngine.createNewGame(gameNumber: gameNumber, matchType: match.type)
                nextGame.currentServer = team
                nextGame.currentServerPlayer = GameRulesEngine.initialServerPlayer(for: gameNumber, team: team)
                match.games.append(nextGame)
                print("🏓 SERVICE SELECTION: Created new game \(gameNumber) with server \(team)")
            }
        }

        // Verberg service selectie en markeer als voltooid
        showingServiceSelection = false
        pendingGameNumber = nil
        serviceSelectionCompleted = true
        print("🏓 SERVICE SELECTION: Hidden service selection and marked as completed")

        // Save the updated match
        dataManager.updateMatch(match)

        // Toon notificatie
        let teamName = getTeamDisplayName(for: team)
        addNotification("\(teamName) starts serving in game \(gameNumber)")
    }

    private func addNotification(_ message: String) {
        withAnimation(.easeInOut(duration: 0.3)) {
            notifications.append(message)
        }

        // Auto-remove notification after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation(.easeInOut(duration: 0.3)) {
                notifications.removeAll { $0 == message }
            }
        }
    }

    /// Announces the next Mix & Match teams using text-to-speech
    private func announceNextMixMatchTeams() {
        guard match.type == .mixMatch else { return }

        // Find the next incomplete game
        guard let nextGame = match.games.first(where: { !$0.isCompleted }),
              let team1Players = nextGame.mixMatchTeam1Players,
              let team2Players = nextGame.mixMatchTeam2Players else {
            print("🎙️ No next Mix & Match game found for announcement")
            return
        }

        print("🎙️ Announcing next Mix & Match teams for game \(nextGame.gameNumber)")
        SoundManager.shared.announceMixMatchTeams(team1Players: team1Players, team2Players: team2Players)
    }

    // MARK: - Portrait Layout

    @ViewBuilder
    private func portraitLayout(game: Game?, isPhone: Bool) -> some View {
        VStack(spacing: 30) {
            // Header - More subtle
            Text("LIVE WEDSTRIJD")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.secondary)
                .padding(.top, 20)

            // Games Stand
            GamesStandView(match: match, isCompact: false, isPhone: isPhone)

            // Game Score
            if let game = game, !showingServiceSelection {
                HStack(spacing: 60) {
                    // Team 1 Score
                    VStack(spacing: 16) {
                        // Team 1 Players
                        VStack(spacing: 8) {
                            let team1Players = getTeamPlayers(for: .team1)
                            if let firstPlayer = team1Players.first {
                                Text(firstPlayer.name)
                                    .font(.system(size: 28, weight: .semibold))
                                    .foregroundColor(.blue)
                            }

                            if team1Players.count > 1 {
                                Text("&")
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(.blue.opacity(0.7))

                                Text(team1Players[1].name)
                                    .font(.system(size: 28, weight: .semibold))
                                    .foregroundColor(.blue)
                            }
                        }

                        Button(action: { addPoint(to: .team1) }) {
                            Text("\(game.team1Score)")
                                .font(.system(size: 120, weight: .bold))
                                .foregroundColor(.blue)
                                .frame(width: 200, height: 200)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .stroke(Color.blue.opacity(0.3), lineWidth: 3)
                                )
                        }
                        .disabled(game.isCompleted)
                        .scaleEffect(game.isCompleted ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: game.isCompleted)
                    }

                    // VS and Game Info
                    VStack(spacing: 16) {
                        Text("VS")
                            .font(.system(size: 36, weight: .bold))
                            .foregroundColor(.secondary)

                        Text("Game \(game.gameNumber)")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.secondary)

                        if game.isDeuce {
                            Text("DEUCE")
                                .font(.system(size: 24, weight: .bold))
                                .foregroundColor(.orange)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color.orange.opacity(0.2))
                                .cornerRadius(12)
                        }
                    }

                    // Team 2 Score
                    VStack(spacing: 16) {
                        // Team 2 Players
                        VStack(spacing: 8) {
                            let team2Players = getTeamPlayers(for: .team2)
                            if let firstPlayer = team2Players.first {
                                Text(firstPlayer.name)
                                    .font(.system(size: 28, weight: .semibold))
                                    .foregroundColor(.red)
                            }

                            if team2Players.count > 1 {
                                Text("&")
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(.red.opacity(0.7))

                                Text(team2Players[1].name)
                                    .font(.system(size: 28, weight: .semibold))
                                    .foregroundColor(.red)
                            }
                        }

                        Button(action: { addPoint(to: .team2) }) {
                            Text("\(game.team2Score)")
                                .font(.system(size: 120, weight: .bold))
                                .foregroundColor(.red)
                                .frame(width: 200, height: 200)
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .stroke(Color.red.opacity(0.3), lineWidth: 3)
                                )
                        }
                        .disabled(game.isCompleted)
                        .scaleEffect(game.isCompleted ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: game.isCompleted)
                    }
                }
            }

            // Service Indicator
            if let game = game, !game.isCompleted, !showingServiceSelection {
                ServiceIndicatorView(game: game, match: match, isCompact: false, isPhone: isPhone)
            }

            // Games Overview - Only on iPad
            if !isPhone && !match.games.isEmpty {
                LiveGamesOverviewView(match: match)
            }

            Spacer()

            // Control Buttons - Much larger for iPad
            HStack(spacing: 50) {
                Button(action: undoLastPoint) {
                    HStack(spacing: 12) {
                        Image(systemName: "arrow.uturn.backward")
                            .font(.system(size: 24))
                        Text("UNDO")
                            .font(.system(size: 24, weight: .bold))
                    }
                    .padding(.horizontal, 40)
                    .padding(.vertical, 20)
                    .background(canUndoPoint() ? Color.orange : Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(16)
                }
                .disabled(!canUndoPoint())

                Button(action: { showingCancelAlert = true }) {
                    HStack(spacing: 12) {
                        Image(systemName: "xmark")
                            .font(.system(size: 24))
                        Text("STOP")
                            .font(.system(size: 24, weight: .bold))
                    }
                    .padding(.horizontal, 40)
                    .padding(.vertical, 20)
                    .background(Color.red)
                    .foregroundColor(.white)
                    .cornerRadius(16)
                }
            }
            .padding(.bottom, 60)
        }
    }

    // Helper function to get team players for current game
    private func getTeamPlayers(for team: Team) -> [Player] {
        if match.type == .mixMatch, let game = currentGame {
            switch team {
            case .team1:
                return game.mixMatchTeam1Players ?? []
            case .team2:
                return game.mixMatchTeam2Players ?? []
            }
        } else {
            switch team {
            case .team1:
                return [match.team1Player1, match.team1Player2].compactMap { $0 }
            case .team2:
                return [match.team2Player1, match.team2Player2].compactMap { $0 }
            }
        }
    }

    private func checkForPendingServiceSelection() {
        print("🔍 PENDING SERVICE CHECK: Checking for pending service selection")

        // Check if match is completed
        if match.isCompleted {
            print("🔍 PENDING SERVICE CHECK: Match is completed, no service selection needed")
            return
        }

        // Find the next game that needs to be played
        let completedGames = match.games.filter { $0.isCompleted }
        let nextGameNumber = completedGames.count + 1

        print("🔍 PENDING SERVICE CHECK: Completed games: \(completedGames.count), next game: \(nextGameNumber)")

        // Check if there's a next game to play
        let totalGamesNeeded = match.bestOfGames
        if nextGameNumber <= totalGamesNeeded {
            // First, check for any games with incorrect gameNumbers and fix them
            for (index, game) in match.games.enumerated() {
                let expectedGameNumber = index + 1
                if game.gameNumber != expectedGameNumber {
                    print("🔍 PENDING SERVICE CHECK: Fixing game at index \(index) - changing gameNumber from \(game.gameNumber) to \(expectedGameNumber)")
                    match.games[index].gameNumber = expectedGameNumber
                }
            }

            // Now check if the next game exists and has no scores yet
            if let nextGame = match.games.first(where: { $0.gameNumber == nextGameNumber }) {
                if nextGame.scoreHistory.isEmpty {
                    print("🔍 PENDING SERVICE CHECK: Found game \(nextGameNumber) without scores - showing service selection")

                    // Show service selection for this game
                    pendingGameNumber = nextGameNumber
                    showingServiceSelection = true
                    serviceSelectionCompleted = false

                    print("🔍 PENDING SERVICE CHECK: Service selection activated for game \(nextGameNumber)")
                } else {
                    print("🔍 PENDING SERVICE CHECK: Game \(nextGameNumber) already has scores")
                }
            } else {
                print("🔍 PENDING SERVICE CHECK: Game \(nextGameNumber) does not exist yet - creating it")

                // Create the next game
                let newGame = Game(gameNumber: nextGameNumber)
                match.games.append(newGame)

                // Update the match in DataManager
                dataManager.updateMatch(match)

                // Show service selection for this new game
                pendingGameNumber = nextGameNumber
                showingServiceSelection = true
                serviceSelectionCompleted = false

                print("🔍 PENDING SERVICE CHECK: Created game \(nextGameNumber) and activated service selection")
            }
        } else {
            print("🔍 PENDING SERVICE CHECK: No more games needed (next would be \(nextGameNumber))")
        }
    }

    // MARK: - iPhone Split-Screen Layout

    @ViewBuilder
    private func iphoneSplitScreenLayout(game: Game?, geometry: GeometryProxy) -> some View {
        if let game = game, !showingServiceSelection {
            HStack(spacing: 0) {
                // Team 1 Half
                teamHalf(
                    team: .team1,
                    game: game,
                    geometry: geometry,
                    isServing: game.currentServer == .team1
                )

                // Team 2 Half
                teamHalf(
                    team: .team2,
                    game: game,
                    geometry: geometry,
                    isServing: game.currentServer == .team2
                )
            }
            .overlay(
                // Pause Menu Button in bottom-right corner
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        pauseMenuButton()
                            .padding(.trailing, 20)
                            .padding(.bottom, 20)
                    }
                }
            )
        } else {
            // Fallback when no game or showing service selection
            Color.black
                .ignoresSafeArea()
        }
    }

    @ViewBuilder
    private func teamHalf(team: Team, game: Game, geometry: GeometryProxy, isServing: Bool) -> some View {
        let backgroundColor = isServing ? Color.red : Color.black
        let teamPlayers = getTeamPlayers(for: team)
        let teamScore = team == .team1 ? game.team1Score : game.team2Score

        Button(action: { addPoint(to: team) }) {
            VStack(spacing: 20) {
                Spacer()

                // Team Names at top
                VStack(spacing: 8) {
                    if let firstPlayer = teamPlayers.first {
                        Text(firstPlayer.name)
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                    }

                    if teamPlayers.count > 1 {
                        Text("&")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))

                        Text(teamPlayers[1].name)
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                    }
                }

                Spacer()

                // Large Score in center
                Text("\(teamScore)")
                    .font(.system(size: 180, weight: .bold))
                    .foregroundColor(.white)

                Spacer()
                Spacer()
            }
            .frame(width: geometry.size.width / 2, height: geometry.size.height)
            .background(backgroundColor)
        }
        .disabled(game.isCompleted)
    }

    @ViewBuilder
    private func pauseMenuButton() -> some View {
        Button(action: { showingPauseMenu = true }) {
            Image(systemName: "pause.circle.fill")
                .font(.system(size: 44))
                .foregroundColor(.white)
                .background(Color.black.opacity(0.6))
                .clipShape(Circle())
        }
    }

    // MARK: - iPad Landscape Layout

    @ViewBuilder
    private func ipadLandscapeLayout(game: Game?, geometry: GeometryProxy) -> some View {
        HStack(spacing: 40) {
            // Left side - Games Stand and Service
            VStack(spacing: 20) {
                // Header
                Text("LIVE MATCH")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)

                // Games Stand
                GamesStandView(match: match, isCompact: true, isPhone: false)

                // Service Indicator
                if let game = game, !game.isCompleted, !showingServiceSelection {
                    ServiceIndicatorView(game: game, match: match, isCompact: true, isPhone: false)
                }

                Spacer()

                // Control Buttons
                HStack(spacing: 20) {
                    Button(action: undoLastPoint) {
                        HStack(spacing: 8) {
                            Image(systemName: "arrow.uturn.backward")
                                .font(.system(size: 18))
                            Text("UNDO")
                                .font(.system(size: 18, weight: .bold))
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(canUndoPoint() ? Color.orange : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                    .disabled(!canUndoPoint())

                    Button(action: { showingCancelAlert = true }) {
                        HStack(spacing: 8) {
                            Image(systemName: "xmark")
                                .font(.system(size: 18))
                            Text("STOP")
                                .font(.system(size: 18, weight: .bold))
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }
                }
            }
            .frame(width: geometry.size.width * 0.25)

            // Right side - Game Scores
            if let game = game, !showingServiceSelection {
                HStack(spacing: 40) {
                    // Team 1 Score
                    VStack(spacing: 12) {
                        // Team 1 Players
                        VStack(spacing: 6) {
                            let team1Players = getTeamPlayers(for: .team1)
                            if let firstPlayer = team1Players.first {
                                Text(firstPlayer.name)
                                    .font(.system(size: 22, weight: .semibold))
                                    .foregroundColor(.blue)
                            }

                            if team1Players.count > 1 {
                                Text("&")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.blue.opacity(0.7))

                                Text(team1Players[1].name)
                                    .font(.system(size: 22, weight: .semibold))
                                    .foregroundColor(.blue)
                            }
                        }

                        Button(action: { addPoint(to: .team1) }) {
                            Text("\(game.team1Score)")
                                .font(.system(size: 100, weight: .bold))
                                .foregroundColor(.blue)
                                .frame(width: 160, height: 160)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(25)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 25)
                                        .stroke(Color.blue.opacity(0.3), lineWidth: 3)
                                )
                        }
                        .disabled(game.isCompleted)
                        .scaleEffect(game.isCompleted ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: game.isCompleted)
                    }

                    // VS and Game Info
                    VStack(spacing: 12) {
                        Text("VS")
                            .font(.system(size: 28, weight: .bold))
                            .foregroundColor(.secondary)

                        Text("Game \(game.gameNumber)")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.secondary)

                        if game.isDeuce {
                            Text("DEUCE")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.orange)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.orange.opacity(0.2))
                                .cornerRadius(8)
                        }
                    }

                    // Team 2 Score
                    VStack(spacing: 12) {
                        // Team 2 Players
                        VStack(spacing: 6) {
                            let team2Players = getTeamPlayers(for: .team2)
                            if let firstPlayer = team2Players.first {
                                Text(firstPlayer.name)
                                    .font(.system(size: 22, weight: .semibold))
                                    .foregroundColor(.red)
                            }

                            if team2Players.count > 1 {
                                Text("&")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.red.opacity(0.7))

                                Text(team2Players[1].name)
                                    .font(.system(size: 22, weight: .semibold))
                                    .foregroundColor(.red)
                            }
                        }

                        Button(action: { addPoint(to: .team2) }) {
                            Text("\(game.team2Score)")
                                .font(.system(size: 100, weight: .bold))
                                .foregroundColor(.red)
                                .frame(width: 160, height: 160)
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(25)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 25)
                                        .stroke(Color.red.opacity(0.3), lineWidth: 3)
                                )
                        }
                        .disabled(game.isCompleted)
                        .scaleEffect(game.isCompleted ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: game.isCompleted)
                    }
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
    }

    // Helper function to get team display name
    private func getTeamDisplayName(for team: Team) -> String {
        return getTeamDisplayName(for: team, game: currentGame)
    }

    // Helper function to get team display name for a specific game
    private func getTeamDisplayName(for team: Team, game: Game?) -> String {
        if match.type == .mixMatch, let game = game {
            // Voor Mix & Match: gebruik spelers uit de specifieke game
            switch team {
            case .team1:
                if let players = game.mixMatchTeam1Players, players.count >= 2 {
                    return "\(players[0].name) & \(players[1].name)"
                }
            case .team2:
                if let players = game.mixMatchTeam2Players, players.count >= 2 {
                    return "\(players[0].name) & \(players[1].name)"
                }
            }
        }

        // Voor reguliere wedstrijden
        switch team {
        case .team1:
            if match.type == .doubles, let player2 = match.team1Player2 {
                return "\(match.team1Player1.name) & \(player2.name)"
            } else {
                return match.team1Player1.name
            }
        case .team2:
            if match.type == .doubles, let player2 = match.team2Player2 {
                return "\(match.team2Player1.name) & \(player2.name)"
            } else {
                return match.team2Player1.name
            }
        }
    }

    // MARK: - Flic Button Integration

    private func setupFlicButtons() {
        // Start live match mode in FlicManager
        flicManager.startLiveMatch(matchId: match.id)

        // Setup button press callbacks
        flicManager.onButtonPress = handleFlicButtonPress
        flicManager.onButtonDoublePress = handleFlicButtonDoublePress

        print("🔘 Flic buttons configured for live match: \(match.id)")
    }

    private func handleFlicButtonPress(_ buttonType: FlicButtonType) {
        print("🔘 FLIC BUTTON PRESSED: \(buttonType)")
        DispatchQueue.main.async {
            // Check if we're in service selection mode
            if showingServiceSelection, let gameNumber = pendingGameNumber {
                print("🔘 FLIC: In service selection mode for game \(gameNumber)")
                switch buttonType {
                case .team1:
                    selectServiceTeam(.team1, for: gameNumber)
                case .team2:
                    selectServiceTeam(.team2, for: gameNumber)
                }
            } else {
                // Normal point scoring
                print("🔘 FLIC: Normal point scoring mode")
                switch buttonType {
                case .team1:
                    print("🔘 FLIC: Adding point to Team 1")
                    addPoint(to: Team.team1)
                case .team2:
                    print("🔘 FLIC: Adding point to Team 2")
                    addPoint(to: Team.team2)
                }
            }
        }
    }

    private func handleFlicButtonDoublePress(_ buttonType: FlicButtonType) {
        print("🔘 FLIC BUTTON DOUBLE PRESSED: \(buttonType)")
        DispatchQueue.main.async {
            // Double press gives point to opponent team
            // Skip service selection mode - double press only works during normal play
            guard !showingServiceSelection else {
                print("🔘 FLIC: Double press ignored during service selection")
                return
            }

            print("🔘 FLIC: Double press - giving point to opponent")
            switch buttonType {
            case .team1:
                // Team 1 button double pressed -> give point to Team 2
                print("🔘 FLIC: Team 1 double pressed - Adding point to Team 2")
                addPoint(to: Team.team2)
            case .team2:
                // Team 2 button double pressed -> give point to Team 1
                print("🔘 FLIC: Team 2 double pressed - Adding point to Team 1")
                addPoint(to: Team.team1)
            }
        }
    }
}

// MARK: - Service Indicator Component

struct ServiceIndicatorView: View {
    let game: Game
    let match: Match
    let isCompact: Bool
    let isPhone: Bool

    var body: some View {
        if isPhone {
            // iPhone: Behoud de volledige layout
            VStack(spacing: 10) {
                // Service header
                Text("🏓 SERVICE")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.secondary)

                // Service players
                HStack(spacing: 20) {
                    // Server
                    VStack(spacing: 6) {
                        Text(serverName)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(game.currentServer == .team1 ? .blue : .red)
                            .multilineTextAlignment(.center)
                            .lineLimit(1)

                        Circle()
                            .fill(game.currentServer == .team1 ? Color.blue : Color.red)
                            .frame(width: 12, height: 12)
                    }
                    .frame(minWidth: 70)

                    // Arrow and text
                    VStack(spacing: 4) {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.orange)

                        Text("SERVEERT")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.orange)
                    }

                    // Receiver
                    VStack(spacing: 6) {
                        Text(receiverName)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(1)

                        Circle()
                            .stroke(Color.gray, lineWidth: 2)
                            .frame(width: 12, height: 12)
                    }
                    .frame(minWidth: 70)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill((game.currentServer == .team1 ? Color.blue : Color.red).opacity(0.1))
                    .stroke((game.currentServer == .team1 ? Color.blue : Color.red).opacity(0.3), lineWidth: 1)
            )
        } else {
            // iPad: Alleen de server
            VStack(spacing: 16) {
                Text("🏓 SERVICE")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.secondary)

                Text(serverName)
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(game.currentServer == .team1 ? .blue : .red)
                    .multilineTextAlignment(.center)
            }
            .frame(minWidth: 200, maxWidth: 200) // Zelfde breedte als BEST OF blok
            .padding(.horizontal, 40)
            .padding(.vertical, 24)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill((game.currentServer == .team1 ? Color.blue : Color.red).opacity(0.1))
                    .stroke((game.currentServer == .team1 ? Color.blue : Color.red).opacity(0.3), lineWidth: 2)
            )
        }
    }

    // Helper computed properties
    private var serverName: String {
        if match.type == .mixMatch {
            // Voor Mix & Match: gebruik spelers uit de huidige game
            switch (game.currentServer, game.currentServerPlayer) {
            case (.team1, 1):
                return game.mixMatchTeam1Players?.first?.name ?? ""
            case (.team1, 2):
                return game.mixMatchTeam1Players?.count ?? 0 > 1 ? game.mixMatchTeam1Players?[1].name ?? "" : ""
            case (.team2, 1):
                return game.mixMatchTeam2Players?.first?.name ?? ""
            case (.team2, 2):
                return game.mixMatchTeam2Players?.count ?? 0 > 1 ? game.mixMatchTeam2Players?[1].name ?? "" : ""
            default:
                return ""
            }
        } else if match.type == .singles {
            // Voor singles: alleen speler 1 van elk team
            return game.currentServer == .team1 ? match.team1Player1.name : match.team2Player1.name
        } else {
            // Voor reguliere dubbels: gebruik vaste spelers
            switch (game.currentServer, game.currentServerPlayer) {
            case (.team1, 1):
                return match.team1Player1.name
            case (.team1, 2):
                return match.team1Player2?.name ?? ""
            case (.team2, 1):
                return match.team2Player1.name
            case (.team2, 2):
                return match.team2Player2?.name ?? ""
            default:
                return ""
            }
        }
    }

    private var receiverName: String {
        let receivingTeam = game.currentServer.opposite

        if match.type == .mixMatch {
            // Voor Mix & Match: gebruik spelers uit de huidige game
            // Bij dubbels rotatie: als server speler 1 is, ontvangt speler 1 van het andere team
            switch (receivingTeam, game.currentServerPlayer) {
            case (.team1, 1):
                return game.mixMatchTeam1Players?.first?.name ?? ""
            case (.team1, 2):
                return game.mixMatchTeam1Players?.count ?? 0 > 1 ? game.mixMatchTeam1Players?[1].name ?? "" : ""
            case (.team2, 1):
                return game.mixMatchTeam2Players?.first?.name ?? ""
            case (.team2, 2):
                return game.mixMatchTeam2Players?.count ?? 0 > 1 ? game.mixMatchTeam2Players?[1].name ?? "" : ""
            default:
                return ""
            }
        } else if match.type == .singles {
            // Voor singles: gebruik vaste spelers
            return receivingTeam == .team1 ? match.team1Player1.name : match.team2Player1.name
        } else {
            // Voor reguliere dubbels: gebruik vaste spelers
            switch (receivingTeam, game.currentServerPlayer) {
            case (.team1, 1):
                return match.team1Player2?.name ?? match.team1Player1.name
            case (.team1, 2):
                return match.team1Player1.name
            case (.team2, 1):
                return match.team2Player2?.name ?? match.team2Player1.name
            case (.team2, 2):
                return match.team2Player1.name
            default:
                return receivingTeam == .team1 ? match.team1Player1.name : match.team2Player1.name
            }
        }
    }
}

// MARK: - Games Stand Component

struct GamesStandView: View {
    let match: Match
    let isCompact: Bool
    let isPhone: Bool

    var body: some View {
        if isPhone {
            // iPhone: Behoud de compacte layout
            VStack(spacing: 4) {
                // Match title
                Text("BEST OF \(match.bestOfGames)")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.secondary)

                // Games stand - No player names on any device
                HStack(spacing: 12) {
                    // Team 1 games
                    VStack(spacing: 2) {
                        Text("\(match.team1GamesWon)")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.blue)
                    }

                    // VS and match info
                    VStack(spacing: 1) {
                        Text("GAMES")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.secondary)

                        Text("VS")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.secondary)
                    }

                    // Team 2 games
                    VStack(spacing: 2) {
                        Text("\(match.team2GamesWon)")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.red)
                    }
                }
            }
            .padding(.horizontal, isCompact ? 16 : 8)
            .padding(.vertical, isCompact ? 12 : 6)
            .background(
                RoundedRectangle(cornerRadius: isCompact ? 12 : 8)
                    .fill(Color.gray.opacity(0.1))
                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
            )
        } else {
            // iPad: Dezelfde grootte als SERVICE blok
            VStack(spacing: 16) {
                Text("BEST OF \(match.bestOfGames)")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.secondary)

                HStack(spacing: 20) {
                    // Team 1 games
                    Text("\(match.team1GamesWon)")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.blue)

                    // VS
                    Text("VS")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.secondary)

                    // Team 2 games
                    Text("\(match.team2GamesWon)")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.red)
                }
            }
            .frame(minWidth: 200, maxWidth: 200) // Zelfde breedte als SERVICE blok
            .padding(.horizontal, 40)
            .padding(.vertical, 24)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.gray.opacity(0.1))
                    .stroke(Color.gray.opacity(0.2), lineWidth: 2)
            )
        }
    }


}

// MARK: - Live Games Overview Component (iPad only)

struct LiveGamesOverviewView: View {
    let match: Match

    // Only show completed games
    private var completedGames: [Game] {
        match.games.filter { $0.isCompleted }
    }

    var body: some View {
        if !completedGames.isEmpty {
            VStack(alignment: .leading, spacing: 16) {
                Text("GESPEELDE GAMES")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.secondary)

                // Games grid - similar to MatchDetailView but more compact for live view
                LazyVGrid(columns: [
                    GridItem(.flexible(), alignment: .leading),
                    GridItem(.flexible(), alignment: .leading),
                    GridItem(.flexible(), alignment: .leading)
                ], alignment: .leading, spacing: 12) {
                    ForEach(completedGames) { game in
                        LiveGameSummaryCard(game: game, match: match)
                    }
                }
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.gray.opacity(0.05))
                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
            )
        }
    }
}

struct LiveGameSummaryCard: View {
    let game: Game
    let match: Match
    @State private var showingScoreChart = false

    var body: some View {
        Button(action: {
            if game.isCompleted && !game.scoreHistory.isEmpty {
                showingScoreChart = true
            }
        }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Game \(game.gameNumber)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                    Spacer()

                    HStack(spacing: 4) {
                        if let winner = game.winner {
                            Circle()
                                .fill(winner == .team1 ? Color.blue : Color.red)
                                .frame(width: 8, height: 8)
                        }

                        // Chart icon voor voltooide games met score history
                        if game.isCompleted && !game.scoreHistory.isEmpty {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                                .font(.caption2)
                                .foregroundColor(.blue)
                        }
                    }
                }

                // Team samenstellingen voor Mix & Match
                if match.type == .mixMatch {
                    VStack(alignment: .leading, spacing: 2) {
                        if let team1Players = game.mixMatchTeam1Players, team1Players.count >= 2 {
                            Text("\(team1Players[0].name) & \(team1Players[1].name)")
                                .font(.caption2)
                                .foregroundColor(.blue)
                                .lineLimit(1)
                        }

                        Text("vs")
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        if let team2Players = game.mixMatchTeam2Players, team2Players.count >= 2 {
                            Text("\(team2Players[0].name) & \(team2Players[1].name)")
                                .font(.caption2)
                                .foregroundColor(.red)
                                .lineLimit(1)
                        }
                    }
                }

                Text("\(game.team1Score) - \(game.team2Score)")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        game.isCompleted && !game.scoreHistory.isEmpty ? Color.blue.opacity(0.3) : Color.clear,
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!game.isCompleted || game.scoreHistory.isEmpty)
        .sheet(isPresented: $showingScoreChart) {
            GameScoreChartView(game: game, match: match)
        }
    }

    // MARK: - Flic Button Integration

}



// MARK: - Game Win Cover View

struct GameWinCoverView: View {
    let winnerText: String
    let onDismiss: () -> Void

    @State private var isVisible = false
    @State private var countdown = 2.5

    var body: some View {
        ZStack {
            // Background
            Color.black.opacity(0.9)
                .ignoresSafeArea()

            VStack(spacing: 40) {
                // Trophy icon
                Image(systemName: "trophy.fill")
                    .font(.system(size: 120))
                    .foregroundColor(.yellow)
                    .scaleEffect(isVisible ? 1.0 : 0.5)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6), value: isVisible)

                // Winner text
                Text(winnerText)
                    .font(.system(size: 48, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.8).delay(0.3), value: isVisible)
                    .shimmer()

                // Countdown
                Text("Volgende game in \(countdown) seconden")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.8).delay(0.6), value: isVisible)

                // Skip button
                Button(action: onDismiss) {
                    Text("Doorgaan")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.black)
                        .padding(.horizontal, 40)
                        .padding(.vertical, 16)
                        .background(Color.white)
                        .cornerRadius(25)
                }
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.8).delay(0.9), value: isVisible)
            }
        }
        .onAppear {
            // Start animations
            withAnimation {
                isVisible = true
            }

            // Start countdown
            startCountdown()
        }
    }

    private func startCountdown() {
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            countdown -= 1

            if countdown <= 0 {
                timer.invalidate()
                onDismiss()
            }
        }
    }
}

// MARK: - Match Win Cover View

struct MatchWinCoverView: View {
    let winnerText: String
    let onDismiss: () -> Void

    @State private var isVisible = false
    @State private var countdown = 5
    @State private var showConfetti = false

    var body: some View {
        ZStack {
            // Background
            Color.black.opacity(0.95)
                .ignoresSafeArea()

            // Confetti
            if showConfetti {
                ConfettiContainerView()
            }

            VStack(spacing: 50) {
                // Crown icon for match win
                Image(systemName: "crown.fill")
                    .font(.system(size: 140))
                    .foregroundColor(.yellow)
                    .shadow(color: .orange, radius: 10)
                    .scaleEffect(isVisible ? 1.0 : 0.3)
                    .animation(.spring(response: 1.0, dampingFraction: 0.5), value: isVisible)

                // Winner text
                Text(winnerText)
                    .font(.system(size: 56, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .shadow(color: .black, radius: 2)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 1.0).delay(0.5), value: isVisible)
                    .shimmer()

                // Celebration text
                Text("🎉 CONGRATULATIONS! 🎉")
                    .font(.system(size: 32, weight: .semibold))
                    .foregroundColor(.yellow)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 1.0).delay(0.8), value: isVisible)

                // Countdown
                Text("Back to overview in \(countdown) seconds")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.8).delay(1.1), value: isVisible)

                // Skip button
                Button(action: onDismiss) {
                    Text("Back to game")
                        .font(.system(size: 22, weight: .semibold))
                        .foregroundColor(.black)
                        .padding(.horizontal, 50)
                        .padding(.vertical, 18)
                        .background(Color.white)
                        .cornerRadius(30)
                        .shadow(color: .black.opacity(0.3), radius: 5)
                }
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.8).delay(1.4), value: isVisible)
            }
        }
        .onAppear {
            // Start animations
            withAnimation {
                isVisible = true
            }

            // Start confetti after crown animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                showConfetti = true
            }

            // Start countdown
            startCountdown()
        }
    }

    private func startCountdown() {
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            countdown -= 1

            if countdown <= 0 {
                timer.invalidate()
                onDismiss()
            }
        }
    }
}

// MARK: - Service Selection Component

struct ServiceSelectionView: View {
    let gameNumber: Int
    let team1Name: String
    let team2Name: String
    let onTeamSelected: (Team) -> Void

    private var isPhone: Bool {
        UIDevice.current.userInterfaceIdiom == .phone
    }

    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.7)
                .ignoresSafeArea()

            VStack(spacing: 30) {
                // Header
                VStack(spacing: isPhone ? 20 : 12) {
                    // Only show table tennis icon on iPad
                    if !isPhone {
                        Text("🏓")
                            .font(.system(size: 60))
                    }

                    Text("Game \(gameNumber)")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.white)

                    Text("Who starts serving?")
                        .font(.system(size: isPhone ? 30 : 20, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)

                    Text("First player to press the button begins!")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                        .multilineTextAlignment(.center)
                }

                // Team Selection Buttons
                HStack(spacing: isPhone ? 50 : 40) {
                    // Team 1 Button
                    Button(action: {
                        print("🎯 SERVICE BUTTON PRESSED: Team 1")
                        onTeamSelected(.team1)
                    }) {
                        VStack(spacing: 16) {
                            Text("🔵")
                                .font(.system(size: 50))

                            Text(team1Name)
                                .font(.system(size: isPhone ? 40 : 20, weight: .semibold))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .frame(
                            width: isPhone ? 300 : 160,
                            height: isPhone ? 180 : 140
                        )
                        .background(Color.blue.opacity(0.3))
                        .cornerRadius(20)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.blue, lineWidth: 3)
                        )
                    }
                    .scaleEffect(1.0)
                    .animation(.easeInOut(duration: 0.1), value: false)

                    Text("VS")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white.opacity(0.6))

                    // Team 2 Button
                    Button(action: {
                        print("🎯 SERVICE BUTTON PRESSED: Team 2")
                        onTeamSelected(.team2)
                    }) {
                        VStack(spacing: 16) {
                            Text("🔴")
                                .font(.system(size: 50))

                            Text(team2Name)
                                .font(.system(size: isPhone ? 40 : 20, weight: .semibold))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .frame(
                            width: isPhone ? 300 : 160,
                            height: isPhone ? 180 : 140
                        )
                        .background(Color.red.opacity(0.3))
                        .cornerRadius(20)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.red, lineWidth: 3)
                        )
                    }
                    .scaleEffect(1.0)
                    .animation(.easeInOut(duration: 0.1), value: false)
                }

                // Instructions
                VStack(spacing: 6) {
                    Text("💡 TIP")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.yellow)

                    Text("Use the flic buttons or press the screen")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
            }
            .padding(40)
        }
        .onAppear {
            print("🎯 SERVICE SELECTION VIEW: Appeared for game \(gameNumber)")
            print("🎯 SERVICE SELECTION VIEW: Team1: \(team1Name), Team2: \(team2Name)")
        }
    }
}
